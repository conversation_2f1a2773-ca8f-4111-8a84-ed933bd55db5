# Fix Critique : Conditions de Course dans les Timers - Crash en Plein Milieu de Match

## 🚨 **PROBLÈME IDENTIFIÉ**

### **Symptômes Observés**
- ✅ Vies réduites à 5, puis 3 → matchs plus longs
- ✅ Crash en plein milieu du 2ème match
- ✅ Crash en plein milieu du 3ème match avec son de sifflet (Round Cancelled)
- ✅ Cache à seulement 11 Mo → pas un problème de mémoire système

### **Cause Racine : Conditions de Course Critiques**

#### 1. **Conflit de Timers Multiples**
```dart
// PROBLÈME : Ces timers peuvent se déclencher simultanément
_createTrackedTimer(Duration(seconds: 3), () {
  _skipRoundDueToError(); // Déclenche Round Cancelled
});

_nextRoundTimer = _createTrackedTimer(const Duration(milliseconds: 3000), () {
  _playNextRound(); // Démarre le round suivant
});
```

#### 2. **Réutilisation Dangereuse de `_nextRoundTimer`**
Le même timer était utilisé pour :
- Démarrer le round suivant après un résultat (3000ms)
- Redémarrer après Round Cancelled (1000ms)
- Démarrer la détection après countdown (1000ms)
- Afficher Game Over (1500ms)

#### 3. **Timers "Anonymes" Non-Trackés**
```dart
// PROBLÈME : Impossible d'annuler ces timers spécifiquement
_createTrackedTimer(Duration(seconds: timeoutSeconds), () {
  _skipRoundDueToError(); // Timer sans référence
});
```

## ✅ **CORRECTIONS APPLIQUÉES**

### **1. Séparation des Timers par Fonction**
```dart
// AVANT : Un seul _nextRoundTimer pour tout
Timer? _nextRoundTimer;

// APRÈS : Timers dédiés pour chaque fonction
Timer? _nextRoundTimer;
Timer? _gestureTimeoutTimer;      // Timeout de détection de gestes
Timer? _subsequentTimeoutTimer;   // Timeout des rounds suivants
Timer? _gameOverTimer;            // Affichage Game Over
```

### **2. Protection contre les Conditions de Course**
```dart
// Annulation préventive avant création
_gestureTimeoutTimer?.cancel();
_gestureTimeoutTimer = _createTrackedTimer(Duration(seconds: timeoutSeconds), () {
  if (mounted && _isDetecting && _detectedGesture == null && !_showingCancelledMessage) {
    _skipRoundDueToError();
  }
  _gestureTimeoutTimer = null; // Nettoyage automatique
});
```

### **3. Délais Augmentés pour Éviter les Conflits**
```dart
// AVANT : 3000ms (conflit avec _resultTimer à 2500ms)
_nextRoundTimer = _createTrackedTimer(const Duration(milliseconds: 3000), () {

// APRÈS : 3500ms (marge de sécurité)
_nextRoundTimer = _createTrackedTimer(const Duration(milliseconds: 3500), () {
```

### **4. Vérifications d'État Renforcées**
```dart
// Vérifications multiples avant exécution
if (mounted && !_showingCancelledMessage && !_roundInProgress) {
  _playNextRound();
} else {
  debugPrint('Skipping - cancelled: $_showingCancelledMessage, inProgress: $_roundInProgress');
}
```

### **5. Nettoyage Automatique des Références**
```dart
_gameOverTimer = _createTrackedTimer(const Duration(milliseconds: 1500), () {
  if (mounted) {
    _displayGameOver();
  }
  _gameOverTimer = null; // ✅ Nettoyage automatique
});
```

## 🔧 **AMÉLIORATIONS AUDIO**

### **Nettoyage Périodique des Ressources Audio**
```dart
/// Nettoyage périodique pour éviter l'accumulation
Future<void> performPeriodicCleanup() async {
  // Nettoyer les files d'attente (max 3 éléments)
  // Réinitialiser les players en état incohérent
  // Vérifier l'état des canaux audio
}
```

### **Intégration dans le Cycle de Maintenance**
```dart
// Nettoyage automatique toutes les 2 minutes
_cacheMaintenanceTimer = _createTrackedPeriodicTimer(
  const Duration(minutes: 2),
  (_) {
    ImageCacheManager.instance.periodicMaintenance();
    _cleanupInactiveTimers();
    gameState.performAudioCleanup(); // ✅ NOUVEAU
  }
);
```

## 📊 **IMPACT DES CORRECTIONS**

### **Avant les Corrections**
- 🔴 **Conditions de course** : Multiples timers en conflit
- 🔴 **Accumulation de ressources** : Timers et audio non nettoyés
- 🔴 **Crash prévisible** : Après 10-15 rounds (matchs longs)

### **Après les Corrections**
- ✅ **Timers isolés** : Chaque fonction a son timer dédié
- ✅ **Nettoyage automatique** : Références nullifiées après usage
- ✅ **Protection renforcée** : Vérifications d'état multiples
- ✅ **Maintenance périodique** : Audio et timers nettoyés automatiquement

## 🎯 **TESTS DE VALIDATION**

### **Test 1 : Match Long (20+ Rounds)**
```bash
# Réduire les vies à 3 et jouer un match complet
flutter run --debug
# Surveiller les logs de timers
flutter logs | grep -E "(TIMER|GAME|Round)"
```

### **Test 2 : Rounds Cancelled Multiples**
```bash
# Forcer plusieurs Round Cancelled de suite
# Vérifier qu'il n'y a pas d'accumulation de timers
flutter logs | grep -E "(Round|sheriff|siflet)"
```

### **Logs Attendus (Normaux)**
```
🕐 TIMER: Created timer (3 active)
✅ TIMER_CLEANUP: All 5 timers cancelled successfully
🧹 AUDIO_MANAGER: Periodic cleanup completed
🔄 GAME: Skipping restart - inProgress: false, cancelled: false
```

### **Logs d'Erreur (À Éviter)**
```
🔴 TIMER: Error in timer callback
⚠️ GAME: Round cancellation already in progress
🔴 AUDIO_MANAGER: Error disposing player
```

## 🏆 **CONCLUSION**

### **Problème Résolu** ✅
Les **conditions de course entre timers** qui causaient les crashes en plein milieu de match sont maintenant **éliminées**.

### **Stabilité Améliorée** ✅
- Timers dédiés pour chaque fonction
- Nettoyage automatique des ressources
- Protection contre les états incohérents
- Maintenance périodique intégrée

### **Prédiction** 🎯
Le jeu devrait maintenant fonctionner de manière **stable** même avec :
- ✅ Vies réduites à 3 (matchs très longs)
- ✅ Multiples Round Cancelled consécutifs
- ✅ Sessions de jeu prolongées (50+ rounds)

**Le crash en plein milieu de match devrait être complètement éliminé.**
