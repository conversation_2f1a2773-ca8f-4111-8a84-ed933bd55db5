# Guide : Connecter votre Device Physique pour Tester

## 📱 **ÉTAPE 1 : PRÉPARER VOTRE DEVICE**

### **Pour Android** 📱
1. **Activer le Mode Développeur** :
   - Allez dans `Paramètres` → `À propos du téléphone`
   - Tapez 7 fois sur `Numéro de build`
   - Message "Vous êtes maintenant développeur !" apparaît

2. **Activer le Débogage USB** :
   - Allez dans `Paramètres` → `Options pour les développeurs`
   - Activez `Débogage USB`
   - Activez `Installer via USB` (si disponible)

3. **Connecter le Câble USB** :
   - Utilisez un câble USB de qualité (pas juste de charge)
   - Connectez votre device à votre Mac

### **Pour iOS** 🍎
1. **Ouvrir Xcode** :
   - Lancez Xcode sur votre Mac
   - Allez dans `Window` → `Devices and Simulators`

2. **Connecter votre iPhone/iPad** :
   - Connectez avec un câble Lightning/USB-C
   - Faites confiance à l'ordinateur sur l'appareil
   - L'appareil apparaît dans Xcode

## 🔧 **ÉTAPE 2 : VÉRIFIER LA CONNEXION FLUTTER**

### **Commandes à Exécuter** 💻
```bash
# 1. Vérifier que Flutter détecte votre device
flutter devices

# 2. Vous devriez voir quelque chose comme :
# Android:
# SM-G973F (mobile) • 1234567890ABCDEF • android-arm64 • Android 11 (API 30)

# iOS:
# iPhone de John (mobile) • 00008030-001234567890123A • ios • iOS 15.0 19A346
```

### **Si Aucun Device n'Apparaît** ⚠️

#### **Android** :
```bash
# Vérifier ADB
adb devices

# Si vide, redémarrer ADB
adb kill-server
adb start-server
adb devices

# Vérifier les drivers USB (Windows seulement)
# Sur Mac, ça devrait marcher directement
```

#### **iOS** :
```bash
# Vérifier que Xcode voit l'appareil
open -a Xcode

# Aller dans Window → Devices and Simulators
# L'appareil doit être listé et "Connected"
```

## 🚀 **ÉTAPE 3 : LANCER LE JEU SUR VOTRE DEVICE**

### **Commande de Lancement** 🎮
```bash
# Naviguer vers le dossier du projet
cd /Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter

# Lancer en mode debug sur le device connecté
flutter run --debug

# Ou spécifier un device particulier si vous en avez plusieurs
flutter run --debug -d [DEVICE_ID]
```

### **Exemple Complet** 📋
```bash
# 1. Vérifier les devices disponibles
flutter devices

# Sortie exemple :
# 2 connected devices:
# SM-G973F (mobile) • 1234567890ABCDEF • android-arm64 • Android 11 (API 30)
# macOS (desktop)   • macos            • darwin-x64     • macOS 12.0.1 21A559

# 2. Lancer sur le device Android
flutter run --debug -d 1234567890ABCDEF

# Ou simplement (Flutter choisira automatiquement le device mobile)
flutter run --debug
```

## 📊 **ÉTAPE 4 : SURVEILLER LES LOGS PENDANT LE TEST**

### **Logs à Surveiller** 🔍
```bash
# Dans un autre terminal, surveiller les logs
flutter logs

# Ou filtrer les logs importants
flutter logs | grep -E "(TIMER|GAME|Round|SIMPLE_AUDIO|SIMPLE_IMAGES)"
```

### **Logs Normaux Attendus** ✅
```
✅ SIMPLE_AUDIO: Initialized with 2 channels
🖼️ SIMPLE_IMAGES: Preloading 7 essential images...
⏰ TIMER: Scheduled action "next_round" in 2800ms
🎯 TIMER: Executing action: next_round
🔧 SIMPLE_MAINTENANCE: Maintenance completed
```

### **Logs d'Erreur à Éviter** ❌
```
🔴 TIMER: Error in timer callback
🔴 SIMPLE_AUDIO: Effect error
🔴 SIMPLE_IMAGES: Cleanup error
⚠️ GAME: Round cancellation already in progress
```

## 🎯 **ÉTAPE 5 : TESTS À EFFECTUER**

### **Test 1 : Match Court (Vies Normales)** ⚡
1. Lancer le jeu avec vies par défaut (3)
2. Jouer un match complet
3. Vérifier qu'il n'y a pas de crash

### **Test 2 : Match Long (Vies Réduites)** 🏃‍♂️
1. Réduire les vies à 3 dans les paramètres
2. Jouer 2-3 matchs consécutifs
3. **C'est ici que ça crashait avant !**
4. Vérifier que ça reste stable

### **Test 3 : Round Cancelled Multiple** 🚨
1. Forcer plusieurs "Round Cancelled" de suite
2. Ne pas faire de gestes devant la caméra
3. Vérifier que le son de sifflet ne cause pas de crash

### **Test 4 : Session Longue** ⏰
1. Jouer pendant 10-15 minutes
2. Surveiller l'utilisation mémoire
3. Vérifier qu'il n'y a pas d'accumulation de ressources

## 🔧 **DÉPANNAGE RAPIDE**

### **Si le Jeu ne se Lance pas** ⚠️
```bash
# Nettoyer et reconstruire
flutter clean
flutter pub get
flutter run --debug
```

### **Si la Caméra ne Marche pas** 📷
```bash
# Vérifier les permissions
# Sur Android : Paramètres → Apps → Rock Paper Scissors → Permissions → Caméra
# Sur iOS : Paramètres → Confidentialité → Caméra → Rock Paper Scissors
```

### **Si les Gestes ne sont pas Détectés** 👋
```bash
# C'est normal sur émulateur !
# Sur device physique, assurez-vous :
# - Bonne luminosité
# - Main bien visible
# - Caméra frontale activée
```

## 🏆 **RÉSULTAT ATTENDU**

Avec nos corrections :
- ✅ **Pas de crash** en plein milieu de match
- ✅ **Transitions fluides** (2.8s au lieu de 3.5s)
- ✅ **Audio stable** (pas de blocage avec le sifflet)
- ✅ **Mémoire stable** (pas d'accumulation)

**Le jeu devrait maintenant être complètement stable, même avec des matchs longs !** 🎯
