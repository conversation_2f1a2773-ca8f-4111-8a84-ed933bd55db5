import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';
import 'dart:async';
import 'dart:collection';

/// Énumération des types de sons pour la gestion de priorité
enum AudioType {
  backgroundMusic,  // Priorité 1 (la plus basse)
  ambientEffect,    // Priorité 2
  gameEffect,       // Priorité 3
  uiEffect,         // Priorité 4
  jackpotEffect,    // Priorité 5
  criticalEffect,   // Priorité 6 (la plus haute)
}

/// Énumération des canaux audio
enum AudioChannel {
  music,      // Canal pour la musique de fond
  effects1,   // Canal principal pour les effets
  effects2,   // Canal secondaire pour les effets
  effects3,   // Canal tertiaire pour les effets
  ui,         // Canal pour les sons d'interface
  jackpot,    // Canal dédié aux sons de jackpot
}

/// Classe représentant une requête audio
class AudioRequest {
  final String filePath;
  final AudioType type;
  final AudioChannel preferredChannel;
  final double volume;
  final bool loop;
  final bool interruptible;
  final Duration? fadeIn;
  final Duration? fadeOut;
  final VoidCallback? onComplete;

  const AudioRequest({
    required this.filePath,
    required this.type,
    this.preferredChannel = AudioChannel.effects1,
    this.volume = 1.0,
    this.loop = false,
    this.interruptible = true,
    this.fadeIn,
    this.fadeOut,
    this.onComplete,
  });

  int get priority => type.index;
}

/// Gestionnaire audio professionnel multi-canaux
class AudioManager {
  static final AudioManager _instance = AudioManager._internal();
  factory AudioManager() => _instance;
  AudioManager._internal();

  // Players pour chaque canal
  final Map<AudioChannel, AudioPlayer> _players = {};

  // État de chaque canal
  final Map<AudioChannel, AudioRequest?> _currentlyPlaying = {};

  // File d'attente pour chaque canal
  final Map<AudioChannel, Queue<AudioRequest>> _queues = {};

  // Cache des fichiers audio préchargés
  final Map<String, Source> _audioCache = {};

  // Volume global et par catégorie
  double _globalVolume = 1.0;
  final Map<AudioType, double> _categoryVolumes = {
    AudioType.backgroundMusic: 0.7,
    AudioType.ambientEffect: 0.8,
    AudioType.gameEffect: 1.0,
    AudioType.uiEffect: 0.9,
    AudioType.jackpotEffect: 1.0,
    AudioType.criticalEffect: 1.0,
  };

  // État d'initialisation
  bool _isInitialized = false;
  bool _isDisposing = false;
  final Completer<void> _initCompleter = Completer<void>();
  
  // Resource tracking
  final Set<AudioPlayer> _allPlayers = <AudioPlayer>{};
  final Map<AudioChannel, StreamSubscription?> _playerSubscriptions = {};

  /// Initialise le gestionnaire audio
  Future<void> initialize() async {
    if (_isInitialized || _isDisposing) return;

    try {
      debugPrint('🎵 AUDIO_MANAGER: Initializing multi-channel audio system...');

      // Créer un player pour chaque canal
      for (AudioChannel channel in AudioChannel.values) {
        try {
          final player = AudioPlayer();
          _players[channel] = player;
          _allPlayers.add(player); // Track for cleanup
          _currentlyPlaying[channel] = null;
          _queues[channel] = Queue<AudioRequest>();

          // Configurer les callbacks pour chaque player
          _setupPlayerCallbacks(channel);
          
          debugPrint('✅ AUDIO_MANAGER: Channel $channel initialized');
        } catch (e) {
          debugPrint('🔴 AUDIO_MANAGER: Error initializing channel $channel: $e');
          // Continue with other channels even if one fails
        }
      }

      // Précharger les sons critiques
      await _preloadCriticalSounds();

      _isInitialized = true;
      _initCompleter.complete();

      debugPrint('✅ AUDIO_MANAGER: Multi-channel audio system initialized successfully');
      debugPrint('🎵 AUDIO_MANAGER: Available channels: ${_players.length}/${AudioChannel.values.length}');

    } catch (e) {
      debugPrint('🔴 AUDIO_MANAGER: Error initializing audio system: $e');
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
    }
  }

  /// Configure les callbacks pour un player
  void _setupPlayerCallbacks(AudioChannel channel) {
    final player = _players[channel];
    if (player == null) return;
    
    try {
      // Cancel existing subscriptions for this channel
      _playerSubscriptions[channel]?.cancel();
      
      // Setup completion listener
      final completeSubscription = player.onPlayerComplete.listen(
        (_) {
          if (!_isDisposing) {
            _onPlayerComplete(channel);
          }
        },
        onError: (error) {
          debugPrint('🔴 AUDIO_MANAGER: Player complete error for channel $channel: $error');
        },
      );
      
      _playerSubscriptions[channel] = completeSubscription;

      // Setup state change listener (less critical, can fail silently)
      try {
        player.onPlayerStateChanged.listen(
          (state) {
            if (!_isDisposing) {
              debugPrint('🎵 AUDIO_MANAGER: Channel $channel state changed to $state');
            }
          },
          onError: (error) {
            debugPrint('🔴 AUDIO_MANAGER: Player state error for channel $channel: $error');
          },
        );
      } catch (e) {
        debugPrint('⚠️ AUDIO_MANAGER: Could not setup state listener for channel $channel: $e');
      }
      
    } catch (e) {
      debugPrint('🔴 AUDIO_MANAGER: Error setting up callbacks for channel $channel: $e');
    }
  }

  /// Gère la fin de lecture d'un canal
  void _onPlayerComplete(AudioChannel channel) {
    if (_isDisposing) return;
    
    try {
      final currentRequest = _currentlyPlaying[channel];
      if (currentRequest != null) {
        debugPrint('🎵 AUDIO_MANAGER: Completed playing ${currentRequest.filePath} on channel $channel');

        // Appeler le callback de fin si défini
        try {
          currentRequest.onComplete?.call();
        } catch (e) {
          debugPrint('🔴 AUDIO_MANAGER: Error in completion callback: $e');
        }

        // Marquer le canal comme libre
        _currentlyPlaying[channel] = null;

        // Ajouter un délai minimal avant de réutiliser le canal (cooldown)
        // Cela évite les problèmes audio et assure une transition fluide
        Future.delayed(const Duration(milliseconds: 100), () {
          if (!_isDisposing) {
            debugPrint('🎵 AUDIO_MANAGER: Cooldown completed for channel $channel');
            // Jouer le prochain son en file d'attente
            _playNextInQueue(channel);
          }
        });
      }
    } catch (e) {
      debugPrint('🔴 AUDIO_MANAGER: Error in _onPlayerComplete for channel $channel: $e');
      // Ensure channel is marked as free even on error
      _currentlyPlaying[channel] = null;
    }
  }

  /// Joue le prochain son en file d'attente pour un canal
  void _playNextInQueue(AudioChannel channel) {
    final queue = _queues[channel]!;
    if (queue.isNotEmpty) {
      final nextRequest = queue.removeFirst();
      _playOnChannel(nextRequest, channel);
    }
  }

  /// Précharge les sons critiques en mémoire
  Future<void> _preloadCriticalSounds() async {
    final criticalSounds = [
      'audio/3_2_1_kids.mp3',
      'audio/you_win.mp3',
      'audio/you_lose.mp3',
      'audio/equal.mp3',
      'audio/gameover.mp3',
      'audio/jackpot_start.mp3',
      'audio/jackpot_end.mp3',
    ];

    debugPrint('🎵 AUDIO_MANAGER: Preloading ${criticalSounds.length} critical sounds...');

    for (String soundPath in criticalSounds) {
      try {
        _audioCache[soundPath] = AssetSource(soundPath);
        debugPrint('✅ AUDIO_MANAGER: Preloaded $soundPath');
      } catch (e) {
        debugPrint('🔴 AUDIO_MANAGER: Failed to preload $soundPath: $e');
      }
    }

    debugPrint('✅ AUDIO_MANAGER: Preloading complete');
  }

  /// Joue un son avec gestion intelligente des canaux
  Future<void> playSound(AudioRequest request) async {
    await _initCompleter.future;

    try {
      debugPrint('🎵 AUDIO_MANAGER: Playing ${request.filePath} (type: ${request.type}, priority: ${request.priority})');

      // Trouver le meilleur canal disponible
      AudioChannel? bestChannel = _findBestChannel(request);

      if (bestChannel != null) {
        await _playOnChannel(request, bestChannel);
      } else {
        // Tous les canaux sont occupés, gérer selon la priorité
        _handleChannelConflict(request);
      }

    } catch (e) {
      debugPrint('🔴 AUDIO_MANAGER: Error playing ${request.filePath}: $e');
    }
  }

  /// Trouve le meilleur canal pour une requête audio
  AudioChannel? _findBestChannel(AudioRequest request) {
    // 1. Vérifier si le canal préféré est prêt
    if (_isChannelReady(request.preferredChannel)) {
      return request.preferredChannel;
    }

    // 2. Chercher un canal prêt du même type
    List<AudioChannel> suitableChannels = _getSuitableChannels(request.type);
    for (AudioChannel channel in suitableChannels) {
      if (_isChannelReady(channel)) {
        return channel;
      }
    }

    // 3. Chercher un canal avec une priorité plus basse
    for (AudioChannel channel in suitableChannels) {
      final currentRequest = _currentlyPlaying[channel];
      if (currentRequest != null &&
          currentRequest.interruptible &&
          currentRequest.priority < request.priority) {
        debugPrint('🔄 AUDIO_MANAGER: Channel $channel can be interrupted (priority: ${currentRequest.priority} < ${request.priority})');
        return channel;
      }
    }

    debugPrint('🔍 AUDIO_MANAGER: No suitable channel found for ${request.filePath}');
    return null;
  }

  /// Retourne les canaux appropriés pour un type de son
  List<AudioChannel> _getSuitableChannels(AudioType type) {
    switch (type) {
      case AudioType.backgroundMusic:
        return [AudioChannel.music];
      case AudioType.jackpotEffect:
        return [AudioChannel.jackpot, AudioChannel.effects1, AudioChannel.effects2];
      case AudioType.uiEffect:
        return [AudioChannel.ui, AudioChannel.effects3];
      default:
        return [AudioChannel.effects1, AudioChannel.effects2, AudioChannel.effects3];
    }
  }

  /// Gère les conflits de canaux
  void _handleChannelConflict(AudioRequest request) {
    debugPrint('🔄 AUDIO_MANAGER: All channels busy, handling conflict for ${request.filePath}');

    // Trouver le canal avec la priorité la plus basse
    AudioChannel? lowestPriorityChannel;
    int lowestPriority = 999;

    List<AudioChannel> suitableChannels = _getSuitableChannels(request.type);
    for (AudioChannel channel in suitableChannels) {
      final currentRequest = _currentlyPlaying[channel];
      if (currentRequest != null &&
          currentRequest.interruptible &&
          currentRequest.priority < lowestPriority) {
        lowestPriority = currentRequest.priority;
        lowestPriorityChannel = channel;
      }
    }

    if (lowestPriorityChannel != null && lowestPriority < request.priority) {
      // Interrompre le son de priorité plus basse
      debugPrint('🔄 AUDIO_MANAGER: Interrupting lower priority sound on channel $lowestPriorityChannel');
      _stopChannel(lowestPriorityChannel);
      _playOnChannel(request, lowestPriorityChannel);
    } else {
      // Ajouter à la file d'attente du canal préféré
      debugPrint('📝 AUDIO_MANAGER: Adding ${request.filePath} to queue for channel ${request.preferredChannel}');
      _queues[request.preferredChannel]!.add(request);
    }
  }

  /// Joue un son sur un canal spécifique
  Future<void> _playOnChannel(AudioRequest request, AudioChannel channel) async {
    try {
      // Vérifier à nouveau si le canal est prêt avant de jouer
      if (!_isChannelReady(channel)) {
        debugPrint('🔴 AUDIO_MANAGER: Channel $channel is not ready, aborting playback of ${request.filePath}');
        return;
      }

      final player = _players[channel]!;

      // Arrêter le son actuel si nécessaire
      if (_currentlyPlaying[channel] != null) {
        debugPrint('🔄 AUDIO_MANAGER: Stopping current sound on channel $channel');
        await player.stop();
      }

      // Marquer le canal comme occupé
      _currentlyPlaying[channel] = request;

      // Obtenir la source audio (cache ou création)
      Source source = _audioCache[request.filePath] ?? AssetSource(request.filePath);

      // Calculer le volume final
      double finalVolume;

      // Les sons critiques (countdown, sifflet) ne sont PAS affectés 
      // par le volume global
      if (request.type == AudioType.criticalEffect) {
        finalVolume = (_categoryVolumes[request.type] ?? 1.0) * request.volume;
      } else {
        // Les autres sons utilisent le volume global
        finalVolume = _globalVolume *
                      (_categoryVolumes[request.type] ?? 1.0) *
                      request.volume;
      }

      // Configurer le player
      await player.setVolume(finalVolume);
      await player.setReleaseMode(request.loop ? ReleaseMode.loop : ReleaseMode.release);

      // Jouer le son
      await player.play(source);

      debugPrint('✅ AUDIO_MANAGER: Playing ${request.filePath} on channel $channel (volume: ${(finalVolume * 100).toInt()}%)');

    } catch (e) {
      debugPrint('🔴 AUDIO_MANAGER: Error playing on channel $channel: $e');
      _currentlyPlaying[channel] = null;
    }
  }

  /// Arrête un canal spécifique
  Future<void> _stopChannel(AudioChannel channel) async {
    try {
      await _players[channel]?.stop();
      _currentlyPlaying[channel] = null;
    } catch (e) {
      debugPrint('🔴 AUDIO_MANAGER: Error stopping channel $channel: $e');
    }
  }

  /// Méthodes de convenance pour jouer différents types de sons
  Future<void> playBackgroundMusic(String filePath, {double volume = 0.7}) async {
    await playSound(AudioRequest(
      filePath: filePath,
      type: AudioType.backgroundMusic,
      preferredChannel: AudioChannel.music,
      volume: volume,
      loop: true,
      interruptible: true,
    ));
  }

  Future<void> playGameEffect(String filePath, {double volume = 1.0}) async {
    await playSound(AudioRequest(
      filePath: filePath,
      type: AudioType.gameEffect,
      preferredChannel: AudioChannel.effects1,
      volume: volume,
      interruptible: true,
    ));
  }

  Future<void> playJackpotEffect(String filePath, {double volume = 1.0}) async {
    await playSound(AudioRequest(
      filePath: filePath,
      type: AudioType.jackpotEffect,
      preferredChannel: AudioChannel.jackpot,
      volume: volume,
      interruptible: false,
    ));
  }

  Future<void> playUIEffect(String filePath, {double volume = 0.9}) async {
    await playSound(AudioRequest(
      filePath: filePath,
      type: AudioType.uiEffect,
      preferredChannel: AudioChannel.ui,
      volume: volume,
      interruptible: true,
    ));
  }

  Future<void> playCriticalEffect(String filePath, {double volume = 1.0}) async {
    await playSound(AudioRequest(
      filePath: filePath,
      type: AudioType.criticalEffect,
      preferredChannel: AudioChannel.effects1,
      volume: volume,
      interruptible: false,
    ));
  }

  /// Contrôle du volume
  Future<void> setGlobalVolume(double volume) async {
    _globalVolume = volume.clamp(0.0, 1.0);

    // Appliquer le nouveau volume seulement aux types NON-critiques
    for (AudioChannel channel in AudioChannel.values) {
      final request = _currentlyPlaying[channel];
      if (request != null) {
        double finalVolume;

        // Les sons critiques (countdown, sifflet) ne sont PAS affectés par le volume global
        if (request.type == AudioType.criticalEffect) {
          finalVolume = (_categoryVolumes[request.type] ?? 1.0) * request.volume;
        } else {
          // Les autres sons (musique, effets normaux) utilisent le volume global
          finalVolume = _globalVolume *
                        (_categoryVolumes[request.type] ?? 1.0) *
                        request.volume;
        }

        await _players[channel]?.setVolume(finalVolume);
      }
    }

    debugPrint('🔊 AUDIO_MANAGER: Global volume set to ${(_globalVolume * 100).toInt()}% (criticalEffect excluded)');
  }

  Future<void> setCategoryVolume(AudioType type, double volume) async {
    _categoryVolumes[type] = volume.clamp(0.0, 1.0);

    // Appliquer le nouveau volume aux players actifs de cette catégorie
    for (AudioChannel channel in AudioChannel.values) {
      final request = _currentlyPlaying[channel];
      if (request != null && request.type == type) {
        double finalVolume;

        // Les sons critiques (countdown, sifflet) ne sont PAS affectés par le volume global
        if (type == AudioType.criticalEffect) {
          finalVolume = (_categoryVolumes[type] ?? 1.0) * request.volume;
        } else {
          // Les autres sons utilisent le volume global
          finalVolume = _globalVolume *
                        (_categoryVolumes[type] ?? 1.0) *
                        request.volume;
        }

        await _players[channel]?.setVolume(finalVolume);
      }
    }

    debugPrint('🔊 AUDIO_MANAGER: ${type.name} volume set to ${(volume * 100).toInt()}%');
  }

  /// Arrêt et nettoyage
  Future<void> stopAll() async {
    debugPrint('🛑 AUDIO_MANAGER: Stopping all audio channels');

    for (AudioChannel channel in AudioChannel.values) {
      await _stopChannel(channel);
      _queues[channel]?.clear();
    }
  }

  Future<void> stopByType(AudioType type) async {
    debugPrint('🛑 AUDIO_MANAGER: Stopping all sounds of type ${type.name}');

    for (AudioChannel channel in AudioChannel.values) {
      final request = _currentlyPlaying[channel];
      if (request != null && request.type == type) {
        await _stopChannel(channel);
      }
    }
  }

  /// Nettoyage périodique des ressources pour éviter l'accumulation
  Future<void> performPeriodicCleanup() async {
    if (_isDisposing) return;

    try {
      debugPrint('🧹 AUDIO_MANAGER: Performing periodic cleanup...');

      // Nettoyer les files d'attente vides ou anciennes
      for (AudioChannel channel in AudioChannel.values) {
        final queue = _queues[channel];
        if (queue != null && queue.isNotEmpty) {
          // Garder seulement les 3 derniers éléments en file d'attente
          while (queue.length > 3) {
            final removed = queue.removeFirst();
            debugPrint('🧹 AUDIO_MANAGER: Removed old queued sound: ${removed.filePath}');
          }
        }
      }

      // Vérifier l'état des players et les réinitialiser si nécessaire
      for (AudioChannel channel in AudioChannel.values) {
        final player = _players[channel];
        if (player != null) {
          try {
            // Si le player est dans un état incohérent, le réinitialiser
            final state = player.state;
            if (state == PlayerState.disposed) {
              debugPrint('🔄 AUDIO_MANAGER: Reinitializing disposed player for channel $channel');
              _players[channel] = AudioPlayer();
              _allPlayers.add(_players[channel]!);
              _setupPlayerCallbacks(channel);
            }
          } catch (e) {
            debugPrint('🔴 AUDIO_MANAGER: Error checking player state for $channel: $e');
          }
        }
      }

      debugPrint('✅ AUDIO_MANAGER: Periodic cleanup completed');
    } catch (e) {
      debugPrint('🔴 AUDIO_MANAGER: Error during periodic cleanup: $e');
    }
  }

  /// Nettoyage des ressources
  Future<void> dispose() async {
    debugPrint('🧹 AUDIO_MANAGER: Disposing audio manager');
    _isDisposing = true;

    // Cancel all subscriptions first
    for (final subscription in _playerSubscriptions.values) {
      try {
        await subscription?.cancel();
      } catch (e) {
        debugPrint('🔴 AUDIO_MANAGER: Error cancelling subscription: $e');
      }
    }
    _playerSubscriptions.clear();

    await stopAll();

    // Dispose all tracked players
    for (AudioPlayer player in _allPlayers) {
      try {
        await player.dispose();
      } catch (e) {
        debugPrint('🔴 AUDIO_MANAGER: Error disposing player: $e');
      }
    }
    _allPlayers.clear();

    _players.clear();
    _currentlyPlaying.clear();
    _queues.clear();
    _audioCache.clear();

    _isInitialized = false;
    debugPrint('✅ AUDIO_MANAGER: Audio manager disposed successfully');
  }

  /// Informations de débogage
  void printStatus() {
    debugPrint('📊 AUDIO_MANAGER STATUS:');
    debugPrint('  Global Volume: ${(_globalVolume * 100).toInt()}%');
    debugPrint('  Active Channels: ${_currentlyPlaying.values.where((r) => r != null).length}/${AudioChannel.values.length}');

    for (AudioChannel channel in AudioChannel.values) {
      final request = _currentlyPlaying[channel];
      final queueSize = _queues[channel]?.length ?? 0;

      if (request != null) {
        debugPrint('  $channel: ${request.filePath} (${request.type.name}) + $queueSize queued');
      } else if (queueSize > 0) {
        debugPrint('  $channel: idle + $queueSize queued');
      } else {
        debugPrint('  $channel: idle');
      }
    }
  }
  /// Vérifie si un canal est prêt pour une nouvelle lecture
  bool _isChannelReady(AudioChannel channel) {
    // Vérifier si le canal existe
    if (!_players.containsKey(channel)) {
      debugPrint('🔴 AUDIO_MANAGER: Channel $channel does not exist');
      return false;
    }

    // Vérifier si le canal est actuellement utilisé
    final currentRequest = _currentlyPlaying[channel];
    if (currentRequest != null) {
      debugPrint('🔴 AUDIO_MANAGER: Channel $channel is currently playing ${currentRequest.filePath}');
      return false;
    }

    // Vérifier si le player est valide
    final player = _players[channel];
    if (player == null) {
      debugPrint('🔴 AUDIO_MANAGER: Player for channel $channel is null');
      return false;
    }

    // Vérifier si le player est dans un état valide
    try {
      debugPrint('✅ AUDIO_MANAGER: Channel $channel is ready');
      return true;
    } catch (e) {
      debugPrint('🔴 AUDIO_MANAGER: Error checking channel $channel readiness: $e');
      return false;
    }
  }

}