import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'dart:async';

/// GESTIONNAIRE DE RESSOURCES SIMPLIFIÉ
/// Sépare complètement Audio, Images et Timers pour éviter les blocages
class SimpleResourceManager {
  static final SimpleResourceManager _instance = SimpleResourceManager._internal();
  factory SimpleResourceManager() => _instance;
  SimpleResourceManager._internal();

  // ========================================
  // 🔊 AUDIO SIMPLIFIÉ (2 canaux seulement)
  // ========================================
  
  final AudioPlayer _musicPlayer = AudioPlayer();
  final AudioPlayer _effectsPlayer = AudioPlayer();
  bool _audioInitialized = false;
  
  /// Initialiser l'audio (simple)
  Future<void> initAudio() async {
    if (_audioInitialized) return;
    
    try {
      await _musicPlayer.setReleaseMode(ReleaseMode.loop);
      await _effectsPlayer.setReleaseMode(ReleaseMode.release);
      _audioInitialized = true;
      debugPrint('✅ SIMPLE_AUDIO: Initialized with 2 channels');
    } catch (e) {
      debugPrint('🔴 SIMPLE_AUDIO: Init error: $e');
    }
  }
  
  /// Jouer musique de fond
  Future<void> playMusic(String path, {double volume = 0.7}) async {
    if (!_audioInitialized) await initAudio();
    
    try {
      await _musicPlayer.stop();
      await _musicPlayer.setVolume(volume);
      await _musicPlayer.play(AssetSource(path));
      debugPrint('🎵 SIMPLE_AUDIO: Playing music: $path');
    } catch (e) {
      debugPrint('🔴 SIMPLE_AUDIO: Music error: $e');
    }
  }
  
  /// Jouer effet sonore (interrompt l'effet précédent)
  Future<void> playEffect(String path, {double volume = 1.0}) async {
    if (!_audioInitialized) await initAudio();
    
    try {
      await _effectsPlayer.stop();
      await _effectsPlayer.setVolume(volume);
      await _effectsPlayer.play(AssetSource(path));
      debugPrint('🔊 SIMPLE_AUDIO: Playing effect: $path');
    } catch (e) {
      debugPrint('🔴 SIMPLE_AUDIO: Effect error: $e');
    }
  }
  
  /// Arrêter tout l'audio
  Future<void> stopAllAudio() async {
    try {
      await _musicPlayer.stop();
      await _effectsPlayer.stop();
      debugPrint('🛑 SIMPLE_AUDIO: All audio stopped');
    } catch (e) {
      debugPrint('🔴 SIMPLE_AUDIO: Stop error: $e');
    }
  }
  
  /// Nettoyer l'audio
  Future<void> disposeAudio() async {
    try {
      await stopAllAudio();
      await _musicPlayer.dispose();
      await _effectsPlayer.dispose();
      _audioInitialized = false;
      debugPrint('🧹 SIMPLE_AUDIO: Audio disposed');
    } catch (e) {
      debugPrint('🔴 SIMPLE_AUDIO: Dispose error: $e');
    }
  }

  // ========================================
  // 🖼️ IMAGES SIMPLIFIÉES (cache minimal)
  // ========================================
  
  final Set<String> _preloadedImages = {};
  
  /// Précharger les images essentielles
  Future<void> preloadEssentialImages(BuildContext context) async {
    final essentialImages = [
      'assets/images/pierre.png',
      'assets/images/feuille.png',
      'assets/images/ciseaux.png',
      'assets/images/you_win.png',
      'assets/images/you_lose.png',
      'assets/images/egal.png',
      'assets/images/sheriff.png',
    ];
    
    debugPrint('🖼️ SIMPLE_IMAGES: Preloading ${essentialImages.length} essential images...');
    
    for (String imagePath in essentialImages) {
      try {
        await precacheImage(AssetImage(imagePath), context);
        _preloadedImages.add(imagePath);
        debugPrint('✅ SIMPLE_IMAGES: Preloaded $imagePath');
      } catch (e) {
        debugPrint('🔴 SIMPLE_IMAGES: Failed to preload $imagePath: $e');
      }
    }
    
    debugPrint('✅ SIMPLE_IMAGES: Preloading complete (${_preloadedImages.length} images)');
  }
  
  /// Nettoyage léger du cache d'images
  void lightImageCleanup() {
    try {
      final imageCache = PaintingBinding.instance.imageCache;
      final currentSize = imageCache.currentSize;
      
      if (currentSize > 30) { // Si plus de 30 images en cache
        imageCache.clear();
        debugPrint('🧹 SIMPLE_IMAGES: Cache cleared (was $currentSize images)');
      }
    } catch (e) {
      debugPrint('🔴 SIMPLE_IMAGES: Cleanup error: $e');
    }
  }
  
  /// Nettoyage complet des images
  void disposeImages() {
    try {
      PaintingBinding.instance.imageCache.clear();
      _preloadedImages.clear();
      debugPrint('🧹 SIMPLE_IMAGES: All images disposed');
    } catch (e) {
      debugPrint('🔴 SIMPLE_IMAGES: Dispose error: $e');
    }
  }

  // ========================================
  // ⏰ MAINTENANCE PÉRIODIQUE (optionnelle)
  // ========================================
  
  Timer? _maintenanceTimer;
  
  /// Démarrer la maintenance automatique (toutes les 3 minutes)
  void startMaintenance() {
    _maintenanceTimer?.cancel();
    _maintenanceTimer = Timer.periodic(const Duration(minutes: 3), (_) {
      _performMaintenance();
    });
    debugPrint('🔧 SIMPLE_MAINTENANCE: Started (every 3 minutes)');
  }
  
  /// Effectuer la maintenance
  void _performMaintenance() {
    try {
      lightImageCleanup();
      debugPrint('🔧 SIMPLE_MAINTENANCE: Maintenance completed');
    } catch (e) {
      debugPrint('🔴 SIMPLE_MAINTENANCE: Error: $e');
    }
  }
  
  /// Arrêter la maintenance
  void stopMaintenance() {
    _maintenanceTimer?.cancel();
    _maintenanceTimer = null;
    debugPrint('🔧 SIMPLE_MAINTENANCE: Stopped');
  }

  // ========================================
  // 🧹 NETTOYAGE COMPLET
  // ========================================
  
  /// Nettoyer toutes les ressources
  Future<void> disposeAll() async {
    debugPrint('🧹 SIMPLE_RESOURCES: Disposing all resources...');
    
    stopMaintenance();
    await disposeAudio();
    disposeImages();
    
    debugPrint('✅ SIMPLE_RESOURCES: All resources disposed');
  }
}
